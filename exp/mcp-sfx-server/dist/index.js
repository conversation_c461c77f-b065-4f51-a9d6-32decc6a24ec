#!/usr/bin/env node
// exp/mcp-sfx-server/index.ts
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { searchSfx, downloadSfx, normalizeSfx } from "./lib/sfx-manager.js";
// Create an MCP server
const server = new McpServer({
    name: "sfx",
    version: "0.1.0"
});
// Search for SFX across multiple providers
server.registerTool("search_sfx", {
    title: "Search SFX",
    description: "Search for SFX across Kenney, Sonniss, Freesound, and OpenGameArt",
    inputSchema: {
        q: z.string().describe("Search query for sound effects"),
        license: z.array(z.enum(["CC0", "CC-BY", "ROYALTY_FREE"])).optional().describe("License filter"),
        limit: z.number().optional().default(10).describe("Maximum number of results")
    }
}, async (params) => {
    const result = await searchSfx(params);
    return {
        content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
            }]
    };
});
// Download and normalize SFX
server.registerTool("download_sfx", {
    title: "Download SFX",
    description: "Download a specific SFX and return a normalized local path",
    inputSchema: {
        id: z.string().describe("SFX ID from search results"),
        source: z.enum(["kenney", "sonniss", "freesound", "opengameart"]).describe("Source provider"),
        format: z.enum(["wav", "ogg"]).optional().default("wav").describe("Output format"),
        mono: z.boolean().optional().default(false).describe("Convert to mono")
    }
}, async (params) => {
    const result = await downloadSfx(params);
    return {
        content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
            }]
    };
});
// Normalize existing audio file
server.registerTool("normalize_sfx", {
    title: "Normalize SFX",
    description: "Normalize an existing audio file to game-ready specs",
    inputSchema: {
        inputPath: z.string().describe("Path to input audio file"),
        outputPath: z.string().optional().describe("Output path (auto-generated if not provided)"),
        format: z.enum(["wav", "ogg"]).optional().default("wav").describe("Output format"),
        mono: z.boolean().optional().default(false).describe("Convert to mono"),
        peakDb: z.number().optional().default(-3).describe("Peak level in dB")
    }
}, async (params) => {
    const result = await normalizeSfx(params);
    return {
        content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
            }]
    };
});
// Catalog of recommended SFX categories for boardgames
server.registerTool("catalog_sfx", {
    title: "SFX Catalog",
    description: "List recommended SFX categories for boardgames",
    inputSchema: {}
}, async () => ({
    content: [{
            type: "text",
            text: JSON.stringify({
                events: {
                    "card/slide": ["ui/swish/soft", "paper/slide", "card/whoosh"],
                    "card/flip": ["ui/flip/cardboard", "paper/flip", "card/turn"],
                    "card/select": ["ui/click/soft", "button/press", "ui/tap"],
                    "card/deselect": ["ui/click/release", "button/release"],
                    "token/place": ["wood/tap/light", "chip/stack", "piece/place"],
                    "token/remove": ["wood/lift", "chip/pickup", "piece/remove"],
                    "score/gain": ["ui/confirm/soft", "bell/dry", "chime/success"],
                    "error": ["ui/error/click", "buzzer/soft", "negative/beep"],
                    "round/start": ["ui/whoosh/medium", "transition/swoosh", "game/start"],
                    "round/end": ["stinger/short/neutral", "transition/end", "game/complete"],
                    "victory": ["fanfare/short", "success/major", "victory/celebration"],
                    "pass": ["ui/decline", "negative/soft", "pass/turn"],
                    "scout": ["action/positive", "ui/special", "move/strategic"],
                    "shuffle": ["paper/shuffle", "cards/mix", "deck/randomize"],
                    "ambient/table": ["room/quiet", "background/subtle", "atmosphere/calm"]
                },
                categories: {
                    "ui": "User interface sounds - clicks, confirmations, errors",
                    "card": "Card-specific sounds - flips, slides, shuffles",
                    "token": "Game piece sounds - placement, removal, stacking",
                    "game": "Game state sounds - round transitions, victory",
                    "ambient": "Background atmosphere sounds"
                }
            }, null, 2)
        }]
}));
// Start the server
async function main() {
    try {
        const transport = new StdioServerTransport();
        await server.connect(transport);
        console.error("SFX MCP Server started successfully");
    }
    catch (error) {
        console.error("Failed to start SFX MCP Server:", error);
        process.exit(1);
    }
}
main();
//# sourceMappingURL=index.js.map