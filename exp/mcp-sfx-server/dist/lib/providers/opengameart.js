import fetch from 'node-fetch';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
// Sample OpenGameArt CC0 sounds (curated collection)
const OPENGAMEART_CATALOG = [
    {
        id: 'oga_ui_click_01',
        title: 'Simple UI Click',
        author: '<PERSON><PERSON>',
        license: 'CC0',
        url: 'https://opengameart.org/sites/default/files/ui_click_simple.ogg',
        tags: ['ui', 'click', 'button', 'interface'],
        description: 'Simple button click sound'
    },
    {
        id: 'oga_card_flip_01',
        title: 'Card Flip Sound',
        author: 'LokiF',
        license: 'CC0',
        url: 'https://opengameart.org/sites/default/files/card_flip.wav',
        tags: ['card', 'flip', 'paper', 'turn'],
        description: 'Card flipping sound effect'
    },
    {
        id: 'oga_success_bell_01',
        title: 'Success Bell',
        author: '<PERSON><PERSON>',
        license: 'CC0',
        url: 'https://opengameart.org/sites/default/files/success_bell.ogg',
        tags: ['success', 'bell', 'positive', 'achievement'],
        description: 'Bell sound for success events'
    },
    {
        id: 'oga_error_beep_01',
        title: '<PERSON>rror Beep',
        author: 'Iwan <PERSON>abovitch',
        license: 'CC0',
        url: 'https://opengameart.org/sites/default/files/error_beep.ogg',
        tags: ['error', 'beep', 'negative', 'fail'],
        description: 'Error notification beep'
    },
    {
        id: 'oga_wood_tap_01',
        title: 'Wood Tap',
        author: 'artisticdude',
        license: 'CC0',
        url: 'https://opengameart.org/sites/default/files/wood_tap.wav',
        tags: ['wood', 'tap', 'impact', 'token', 'piece'],
        description: 'Light wood tapping sound'
    },
    {
        id: 'oga_paper_rustle_01',
        title: 'Paper Rustle',
        author: 'Iwan Gabovitch',
        license: 'CC0',
        url: 'https://opengameart.org/sites/default/files/paper_rustle.ogg',
        tags: ['paper', 'rustle', 'shuffle', 'cards'],
        description: 'Paper rustling/shuffling sound'
    },
    {
        id: 'oga_whoosh_01',
        title: 'Whoosh Transition',
        author: 'Kenney',
        license: 'CC0',
        url: 'https://opengameart.org/sites/default/files/whoosh_transition.ogg',
        tags: ['whoosh', 'transition', 'air', 'movement'],
        description: 'Whoosh sound for transitions'
    }
];
export async function searchOpenGameArt(query, licenses = ['CC0', 'CC-BY'], limit = 10) {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/);
    const results = OPENGAMEART_CATALOG
        .filter(sound => {
        // Filter by license
        if (!licenses.includes(sound.license))
            return false;
        // Filter by query
        const searchText = `${sound.title} ${sound.tags.join(' ')} ${sound.description || ''}`.toLowerCase();
        return queryWords.some(word => searchText.includes(word));
    })
        .map(sound => ({
        id: sound.id,
        title: sound.title,
        description: `${sound.description} - By ${sound.author}`,
        source: 'opengameart',
        license: sound.license,
        url: sound.url,
        tags: sound.tags
    }))
        .slice(0, limit);
    return results;
}
export async function downloadOpenGameArt(id) {
    const sound = OPENGAMEART_CATALOG.find(s => s.id === id);
    if (!sound) {
        throw new Error(`OpenGameArt sound not found: ${id}`);
    }
    // Create cache directory
    const cacheDir = path.join(os.homedir(), '.game-sfx-cache', 'opengameart');
    await fs.mkdir(cacheDir, { recursive: true });
    const extension = path.extname(sound.url) || '.ogg';
    const filename = `${sound.id}${extension}`;
    const filePath = path.join(cacheDir, filename);
    // Check if already downloaded
    try {
        await fs.access(filePath);
        console.error(`Using cached OpenGameArt file: ${filePath}`);
        return {
            path: filePath,
            metadata: {
                title: sound.title,
                license: sound.license,
                source: 'opengameart',
                author: sound.author,
                tags: sound.tags,
                description: sound.description,
                originalUrl: sound.url
            }
        };
    }
    catch {
        // File doesn't exist, download it
    }
    console.error(`Downloading OpenGameArt sound: ${sound.url}`);
    try {
        const response = await fetch(sound.url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const buffer = await response.buffer();
        await fs.writeFile(filePath, buffer);
        console.error(`Downloaded OpenGameArt sound to: ${filePath}`);
        return {
            path: filePath,
            metadata: {
                title: sound.title,
                license: sound.license,
                source: 'opengameart',
                author: sound.author,
                tags: sound.tags,
                description: sound.description,
                originalUrl: sound.url,
                fileSize: buffer.length
            }
        };
    }
    catch (error) {
        throw new Error(`Failed to download OpenGameArt sound: ${error}`);
    }
}
//# sourceMappingURL=opengameart.js.map