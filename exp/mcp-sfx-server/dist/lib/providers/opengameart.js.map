{"version": 3, "file": "opengameart.js", "sourceRoot": "", "sources": ["../../../lib/providers/opengameart.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,YAAY,CAAC;AAC/B,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,MAAM,IAAI,CAAC;AAgBpB,qDAAqD;AACrD,MAAM,mBAAmB,GAAqB;IAC5C;QACE,EAAE,EAAE,iBAAiB;QACrB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,QAAQ;QAChB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,iEAAiE;QACtE,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC;QAC5C,WAAW,EAAE,2BAA2B;KACzC;IACD;QACE,EAAE,EAAE,kBAAkB;QACtB,KAAK,EAAE,iBAAiB;QACxB,MAAM,EAAE,OAAO;QACf,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,2DAA2D;QAChE,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;QACvC,WAAW,EAAE,4BAA4B;KAC1C;IACD;QACE,EAAE,EAAE,qBAAqB;QACzB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,gBAAgB;QACxB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,8DAA8D;QACnE,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC;QACpD,WAAW,EAAE,+BAA+B;KAC7C;IACD;QACE,EAAE,EAAE,mBAAmB;QACvB,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,gBAAgB;QACxB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,4DAA4D;QACjE,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;QAC3C,WAAW,EAAE,yBAAyB;KACvC;IACD;QACE,EAAE,EAAE,iBAAiB;QACrB,KAAK,EAAE,UAAU;QACjB,MAAM,EAAE,cAAc;QACtB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,0DAA0D;QAC/D,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;QACjD,WAAW,EAAE,0BAA0B;KACxC;IACD;QACE,EAAE,EAAE,qBAAqB;QACzB,KAAK,EAAE,cAAc;QACrB,MAAM,EAAE,gBAAgB;QACxB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,8DAA8D;QACnE,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;QAC7C,WAAW,EAAE,gCAAgC;KAC9C;IACD;QACE,EAAE,EAAE,eAAe;QACnB,KAAK,EAAE,mBAAmB;QAC1B,MAAM,EAAE,QAAQ;QAChB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,mEAAmE;QACxE,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC;QACjD,WAAW,EAAE,8BAA8B;KAC5C;CACF,CAAC;AAEF,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,KAAa,EACb,WAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,EACrC,QAAgB,EAAE;IAElB,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IACvC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAE3C,MAAM,OAAO,GAAG,mBAAmB;SAChC,MAAM,CAAC,KAAK,CAAC,EAAE;QACd,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC;YAAE,OAAO,KAAK,CAAC;QAEpD,kBAAkB;QAClB,MAAM,UAAU,GAAG,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC,WAAW,EAAE,CAAC;QACrG,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC;SACD,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACb,EAAE,EAAE,KAAK,CAAC,EAAE;QACZ,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,WAAW,EAAE,GAAG,KAAK,CAAC,WAAW,SAAS,KAAK,CAAC,MAAM,EAAE;QACxD,MAAM,EAAE,aAAa;QACrB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,GAAG,EAAE,KAAK,CAAC,GAAG;QACd,IAAI,EAAE,KAAK,CAAC,IAAI;KACjB,CAAC,CAAC;SACF,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAEnB,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,EAAU;IAClD,MAAM,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IACzD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAED,yBAAyB;IACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;IAC3E,MAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAE9C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC;IACpD,MAAM,QAAQ,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;IAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAE/C,8BAA8B;IAC9B,IAAI,CAAC;QACH,MAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1B,OAAO,CAAC,KAAK,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC5D,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE;gBACR,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,MAAM,EAAE,aAAa;gBACrB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,WAAW,EAAE,KAAK,CAAC,GAAG;aACvB;SACF,CAAC;IACJ,CAAC;IAAC,MAAM,CAAC;QACP,kCAAkC;IACpC,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;IAE7D,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;QACvC,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErC,OAAO,CAAC,KAAK,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;QAE9D,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE;gBACR,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,MAAM,EAAE,aAAa;gBACrB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,WAAW,EAAE,KAAK,CAAC,GAAG;gBACtB,QAAQ,EAAE,MAAM,CAAC,MAAM;aACxB;SACF,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;IACpE,CAAC;AACH,CAAC"}