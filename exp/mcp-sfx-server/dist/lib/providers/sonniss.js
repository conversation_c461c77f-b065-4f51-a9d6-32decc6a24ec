import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
// Sample Sonniss-style sounds (in production, this would be a comprehensive catalog)
const SONNISS_CATALOG = [
    {
        id: 'sonniss_ui_button_click_01',
        title: 'Button Click Soft',
        pack: 'UI Essentials',
        category: 'Interface',
        tags: ['ui', 'button', 'click', 'soft', 'interface'],
        filename: 'UI_Button_Click_Soft_01.wav'
    },
    {
        id: 'sonniss_ui_button_click_02',
        title: 'Button Click Sharp',
        pack: 'UI Essentials',
        category: 'Interface',
        tags: ['ui', 'button', 'click', 'sharp', 'interface'],
        filename: 'UI_Button_Click_Sharp_01.wav'
    },
    {
        id: 'sonniss_card_flip_01',
        title: 'Card Flip Paper',
        pack: 'Foley Collection',
        category: 'Paper',
        tags: ['card', 'flip', 'paper', 'turn', 'foley'],
        filename: 'Card_Flip_Paper_01.wav'
    },
    {
        id: 'sonniss_card_slide_01',
        title: 'Card Slide Smooth',
        pack: 'Foley Collection',
        category: 'Paper',
        tags: ['card', 'slide', 'smooth', 'paper', 'foley'],
        filename: 'Card_Slide_Smooth_01.wav'
    },
    {
        id: 'sonniss_wood_tap_light_01',
        title: 'Wood Tap Light',
        pack: 'Material Impacts',
        category: 'Wood',
        tags: ['wood', 'tap', 'light', 'impact', 'token'],
        filename: 'Wood_Tap_Light_01.wav'
    },
    {
        id: 'sonniss_success_chime_01',
        title: 'Success Chime Soft',
        pack: 'Game States',
        category: 'Success',
        tags: ['success', 'chime', 'positive', 'achievement', 'soft'],
        filename: 'Success_Chime_Soft_01.wav'
    },
    {
        id: 'sonniss_error_buzz_01',
        title: 'Error Buzz Soft',
        pack: 'Game States',
        category: 'Error',
        tags: ['error', 'buzz', 'negative', 'fail', 'soft'],
        filename: 'Error_Buzz_Soft_01.wav'
    },
    {
        id: 'sonniss_whoosh_medium_01',
        title: 'Whoosh Medium',
        pack: 'Transitions',
        category: 'Movement',
        tags: ['whoosh', 'medium', 'transition', 'air', 'movement'],
        filename: 'Whoosh_Medium_01.wav'
    },
    {
        id: 'sonniss_stinger_neutral_01',
        title: 'Stinger Neutral Short',
        pack: 'Musical Elements',
        category: 'Stinger',
        tags: ['stinger', 'neutral', 'short', 'musical', 'transition'],
        filename: 'Stinger_Neutral_Short_01.wav'
    },
    {
        id: 'sonniss_shuffle_paper_01',
        title: 'Paper Shuffle Cards',
        pack: 'Foley Collection',
        category: 'Paper',
        tags: ['paper', 'shuffle', 'cards', 'rustle', 'foley'],
        filename: 'Paper_Shuffle_Cards_01.wav'
    }
];
export async function searchSonniss(query, limit = 10) {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/);
    const results = SONNISS_CATALOG
        .filter(sound => {
        const searchText = `${sound.title} ${sound.category} ${sound.tags.join(' ')}`.toLowerCase();
        return queryWords.some(word => searchText.includes(word));
    })
        .map(sound => ({
        id: sound.id,
        title: sound.title,
        description: `${sound.pack} - ${sound.category}`,
        source: 'sonniss',
        license: 'ROYALTY_FREE',
        tags: sound.tags
    }))
        .slice(0, limit);
    return results;
}
export async function downloadSonniss(id) {
    const sound = SONNISS_CATALOG.find(s => s.id === id);
    if (!sound) {
        throw new Error(`Sonniss sound not found: ${id}`);
    }
    // Create cache directory
    const cacheDir = path.join(os.homedir(), '.game-sfx-cache', 'sonniss');
    await fs.mkdir(cacheDir, { recursive: true });
    const filename = `${sound.id}.wav`;
    const filePath = path.join(cacheDir, filename);
    // Check if already downloaded
    try {
        await fs.access(filePath);
        console.error(`Using cached Sonniss file: ${filePath}`);
        return {
            path: filePath,
            metadata: {
                title: sound.title,
                license: 'ROYALTY_FREE',
                source: 'sonniss',
                pack: sound.pack,
                category: sound.category,
                tags: sound.tags
            }
        };
    }
    catch {
        // File doesn't exist, need to create a placeholder or mock
    }
    // In a real implementation, this would download from Sonniss
    // For now, we'll create a placeholder file or use a mock sound
    console.error(`Sonniss download not implemented for: ${id}`);
    console.error(`This would require Sonniss licensing and access to their catalog`);
    // Create a placeholder file to prevent errors
    const placeholderContent = Buffer.from('PLACEHOLDER_SONNISS_AUDIO_FILE');
    await fs.writeFile(filePath, placeholderContent);
    return {
        path: filePath,
        metadata: {
            title: sound.title,
            license: 'ROYALTY_FREE',
            source: 'sonniss',
            pack: sound.pack,
            category: sound.category,
            tags: sound.tags,
            note: 'Placeholder - requires Sonniss licensing for actual audio'
        }
    };
}
//# sourceMappingURL=sonniss.js.map