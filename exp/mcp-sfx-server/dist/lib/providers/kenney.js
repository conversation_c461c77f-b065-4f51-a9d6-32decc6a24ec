import fetch from 'node-fetch';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
// Kenney UI Audio pack - CC0 licensed
const KENNEY_UI_SOUNDS = [
    {
        id: 'kenney_ui_click_001',
        title: 'UI Click 001',
        pack: 'UI Audio',
        filename: 'click_001.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_click_001.ogg',
        tags: ['ui', 'click', 'button', 'interface']
    },
    {
        id: 'kenney_ui_click_002',
        title: 'UI Click 002',
        pack: 'UI Audio',
        filename: 'click_002.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_click_002.ogg',
        tags: ['ui', 'click', 'button', 'interface']
    },
    {
        id: 'kenney_ui_click_003',
        title: 'UI Click 003',
        pack: 'UI Audio',
        filename: 'click_003.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_click_003.ogg',
        tags: ['ui', 'click', 'button', 'interface']
    },
    {
        id: 'kenney_ui_confirmation_001',
        title: 'UI Confirmation 001',
        pack: 'UI Audio',
        filename: 'confirmation_001.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_confirmation_001.ogg',
        tags: ['ui', 'confirmation', 'success', 'positive']
    },
    {
        id: 'kenney_ui_confirmation_002',
        title: 'UI Confirmation 002',
        pack: 'UI Audio',
        filename: 'confirmation_002.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_confirmation_002.ogg',
        tags: ['ui', 'confirmation', 'success', 'positive']
    },
    {
        id: 'kenney_ui_error_001',
        title: 'UI Error 001',
        pack: 'UI Audio',
        filename: 'error_001.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_error_001.ogg',
        tags: ['ui', 'error', 'negative', 'fail']
    },
    {
        id: 'kenney_ui_error_002',
        title: 'UI Error 002',
        pack: 'UI Audio',
        filename: 'error_002.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_error_002.ogg',
        tags: ['ui', 'error', 'negative', 'fail']
    },
    {
        id: 'kenney_ui_switch_001',
        title: 'UI Switch 001',
        pack: 'UI Audio',
        filename: 'switch_001.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_switch_001.ogg',
        tags: ['ui', 'switch', 'toggle', 'flip']
    },
    {
        id: 'kenney_ui_switch_002',
        title: 'UI Switch 002',
        pack: 'UI Audio',
        filename: 'switch_002.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_switch_002.ogg',
        tags: ['ui', 'switch', 'toggle', 'flip']
    },
    {
        id: 'kenney_ui_whoosh_001',
        title: 'UI Whoosh 001',
        pack: 'UI Audio',
        filename: 'whoosh_001.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_whoosh_001.ogg',
        tags: ['ui', 'whoosh', 'transition', 'swipe']
    },
    {
        id: 'kenney_ui_whoosh_002',
        title: 'UI Whoosh 002',
        pack: 'UI Audio',
        filename: 'whoosh_002.ogg',
        url: 'https://kenney.nl/content/3-assets/97-ui-audio/ui_whoosh_002.ogg',
        tags: ['ui', 'whoosh', 'transition', 'swipe']
    }
];
export async function searchKenney(query, limit = 10) {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/);
    const results = KENNEY_UI_SOUNDS
        .filter(sound => {
        const searchText = `${sound.title} ${sound.tags.join(' ')}`.toLowerCase();
        return queryWords.some(word => searchText.includes(word));
    })
        .map(sound => ({
        id: sound.id,
        title: sound.title,
        description: `${sound.pack} - ${sound.filename}`,
        source: 'kenney',
        license: 'CC0',
        url: sound.url,
        tags: sound.tags
    }))
        .slice(0, limit);
    return results;
}
export async function downloadKenney(id) {
    const sound = KENNEY_UI_SOUNDS.find(s => s.id === id);
    if (!sound) {
        throw new Error(`Kenney sound not found: ${id}`);
    }
    // Create cache directory
    const cacheDir = path.join(os.homedir(), '.game-sfx-cache', 'kenney');
    await fs.mkdir(cacheDir, { recursive: true });
    const filename = `${sound.id}.ogg`;
    const filePath = path.join(cacheDir, filename);
    // Check if already downloaded
    try {
        await fs.access(filePath);
        console.error(`Using cached Kenney file: ${filePath}`);
        return {
            path: filePath,
            metadata: {
                title: sound.title,
                license: 'CC0',
                source: 'kenney',
                pack: sound.pack,
                tags: sound.tags,
                originalUrl: sound.url
            }
        };
    }
    catch {
        // File doesn't exist, download it
    }
    console.error(`Downloading Kenney sound: ${sound.url}`);
    try {
        const response = await fetch(sound.url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const buffer = await response.buffer();
        await fs.writeFile(filePath, buffer);
        console.error(`Downloaded Kenney sound to: ${filePath}`);
        return {
            path: filePath,
            metadata: {
                title: sound.title,
                license: 'CC0',
                source: 'kenney',
                pack: sound.pack,
                tags: sound.tags,
                originalUrl: sound.url,
                fileSize: buffer.length
            }
        };
    }
    catch (error) {
        throw new Error(`Failed to download Kenney sound: ${error}`);
    }
}
//# sourceMappingURL=kenney.js.map