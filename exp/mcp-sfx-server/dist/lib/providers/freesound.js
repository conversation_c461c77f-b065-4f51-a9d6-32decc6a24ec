import fetch from 'node-fetch';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
// Freesound.org API integration
// Note: This is a simplified implementation. In production, you'd need:
// 1. API key registration at freesound.org
// 2. OAuth authentication for downloads
// 3. Rate limiting and proper error handling
const FREESOUND_API_BASE = 'https://freesound.org/apiv2';
const FREESOUND_API_KEY = process.env.FREESOUND_API_KEY || '';
export async function searchFreesound(query, licenses = ['CC0', 'CC-BY'], limit = 10) {
    if (!FREESOUND_API_KEY) {
        console.error('Freesound API key not configured, returning empty results');
        return [];
    }
    try {
        // Map license filters to Freesound license names
        const freesoundLicenses = licenses.map(license => {
            switch (license) {
                case 'CC0': return 'Creative Commons 0';
                case 'CC-BY': return 'Attribution';
                default: return license;
            }
        });
        const searchParams = new URLSearchParams({
            query,
            token: FREESOUND_API_KEY,
            page_size: limit.toString(),
            fields: 'id,name,description,username,license,download,previews,tags,duration,filesize',
            filter: `license:"${freesoundLicenses.join('" OR license:"')}"`,
            sort: 'score'
        });
        const response = await fetch(`${FREESOUND_API_BASE}/search/text/?${searchParams}`);
        if (!response.ok) {
            throw new Error(`Freesound API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        return data.results.map(sound => ({
            id: `freesound_${sound.id}`,
            title: sound.name,
            description: sound.description || `By ${sound.username}`,
            source: 'freesound',
            license: mapFreesoundLicense(sound.license),
            url: sound.previews['preview-hq-ogg'] || sound.previews['preview-lq-ogg'],
            tags: sound.tags,
            duration: sound.duration,
            fileSize: sound.filesize
        }));
    }
    catch (error) {
        console.error('Freesound search error:', error);
        return [];
    }
}
export async function downloadFreesound(id) {
    if (!FREESOUND_API_KEY) {
        throw new Error('Freesound API key not configured');
    }
    const soundId = id.replace('freesound_', '');
    // Create cache directory
    const cacheDir = path.join(os.homedir(), '.game-sfx-cache', 'freesound');
    await fs.mkdir(cacheDir, { recursive: true });
    const filename = `${id}.ogg`;
    const filePath = path.join(cacheDir, filename);
    // Check if already downloaded
    try {
        await fs.access(filePath);
        console.error(`Using cached Freesound file: ${filePath}`);
        // Load metadata from cache
        const metadataPath = path.join(cacheDir, `${id}.json`);
        const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf-8'));
        return { path: filePath, metadata };
    }
    catch {
        // File doesn't exist, download it
    }
    try {
        // Get sound details
        const detailsResponse = await fetch(`${FREESOUND_API_BASE}/sounds/${soundId}/?token=${FREESOUND_API_KEY}&fields=id,name,description,username,license,previews,tags,duration,filesize`);
        if (!detailsResponse.ok) {
            throw new Error(`Freesound API error: ${detailsResponse.status}`);
        }
        const soundDetails = await detailsResponse.json();
        // Download preview (high quality OGG)
        const previewUrl = soundDetails.previews['preview-hq-ogg'] || soundDetails.previews['preview-lq-ogg'];
        if (!previewUrl) {
            throw new Error('No preview available for this sound');
        }
        console.error(`Downloading Freesound preview: ${previewUrl}`);
        const audioResponse = await fetch(previewUrl);
        if (!audioResponse.ok) {
            throw new Error(`Failed to download audio: ${audioResponse.status}`);
        }
        const buffer = await audioResponse.buffer();
        await fs.writeFile(filePath, buffer);
        const metadata = {
            title: soundDetails.name,
            description: soundDetails.description,
            username: soundDetails.username,
            license: mapFreesoundLicense(soundDetails.license),
            source: 'freesound',
            tags: soundDetails.tags,
            duration: soundDetails.duration,
            fileSize: buffer.length,
            originalId: soundDetails.id
        };
        // Save metadata
        const metadataPath = path.join(cacheDir, `${id}.json`);
        await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));
        console.error(`Downloaded Freesound sound to: ${filePath}`);
        return { path: filePath, metadata };
    }
    catch (error) {
        throw new Error(`Failed to download Freesound sound: ${error}`);
    }
}
function mapFreesoundLicense(license) {
    if (license.includes('Creative Commons 0'))
        return 'CC0';
    if (license.includes('Attribution'))
        return 'CC-BY';
    return license;
}
//# sourceMappingURL=freesound.js.map