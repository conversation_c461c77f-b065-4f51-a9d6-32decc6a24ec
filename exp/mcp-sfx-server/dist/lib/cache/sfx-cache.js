import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
import crypto from 'crypto';
export class SfxCache {
    cacheDir;
    indexPath;
    index;
    constructor(cacheDir) {
        this.cacheDir = cacheDir || path.join(os.homedir(), '.game-sfx-cache');
        this.indexPath = path.join(this.cacheDir, 'index.json');
        this.index = {
            entries: {},
            version: '1.0.0',
            lastUpdated: new Date().toISOString()
        };
    }
    async initialize() {
        // Ensure cache directory exists
        await fs.mkdir(this.cacheDir, { recursive: true });
        // Load existing index
        try {
            const indexData = await fs.readFile(this.indexPath, 'utf-8');
            this.index = JSON.parse(indexData);
        }
        catch (error) {
            // Index doesn't exist or is corrupted, start fresh
            console.error('Cache index not found or corrupted, creating new one');
            await this.saveIndex();
        }
    }
    async saveIndex() {
        this.index.lastUpdated = new Date().toISOString();
        await fs.writeFile(this.indexPath, JSON.stringify(this.index, null, 2));
    }
    getCacheKey(id, source) {
        return `${source}:${id}`;
    }
    async calculateFileHash(filePath) {
        const fileBuffer = await fs.readFile(filePath);
        return crypto.createHash('sha256').update(fileBuffer).digest('hex');
    }
    async get(id, source) {
        await this.initialize();
        const key = this.getCacheKey(id, source);
        const entry = this.index.entries[key];
        if (!entry) {
            return null;
        }
        // Check if file still exists
        try {
            await fs.access(entry.path);
            // Update last accessed time
            entry.lastAccessed = new Date().toISOString();
            await this.saveIndex();
            return {
                path: entry.path,
                originalPath: entry.originalPath,
                metadata: entry.metadata
            };
        }
        catch {
            // File no longer exists, remove from cache
            delete this.index.entries[key];
            await this.saveIndex();
            return null;
        }
    }
    async set(id, source, result) {
        await this.initialize();
        const key = this.getCacheKey(id, source);
        // Calculate file hash for integrity checking
        const hash = await this.calculateFileHash(result.path);
        // Get file size
        const stats = await fs.stat(result.path);
        const entry = {
            id,
            source,
            path: result.path,
            originalPath: result.originalPath,
            metadata: result.metadata,
            license: result.metadata.license || 'UNKNOWN',
            hash,
            createdAt: new Date().toISOString(),
            lastAccessed: new Date().toISOString(),
            fileSize: stats.size
        };
        this.index.entries[key] = entry;
        await this.saveIndex();
        console.error(`Cached SFX: ${key} -> ${result.path}`);
    }
    async list(source) {
        await this.initialize();
        const entries = Object.values(this.index.entries);
        if (source) {
            return entries.filter(entry => entry.source === source);
        }
        return entries;
    }
    async remove(id, source) {
        await this.initialize();
        const key = this.getCacheKey(id, source);
        const entry = this.index.entries[key];
        if (!entry) {
            return false;
        }
        // Remove files
        try {
            await fs.unlink(entry.path);
            if (entry.originalPath !== entry.path) {
                await fs.unlink(entry.originalPath);
            }
        }
        catch (error) {
            console.error(`Failed to remove cached files: ${error}`);
        }
        // Remove from index
        delete this.index.entries[key];
        await this.saveIndex();
        return true;
    }
    async cleanup(maxAge = 30 * 24 * 60 * 60 * 1000) {
        await this.initialize();
        const now = Date.now();
        let removedCount = 0;
        for (const [key, entry] of Object.entries(this.index.entries)) {
            const lastAccessed = new Date(entry.lastAccessed).getTime();
            const age = now - lastAccessed;
            if (age > maxAge) {
                const [source, id] = key.split(':', 2);
                if (await this.remove(id, source)) {
                    removedCount++;
                }
            }
        }
        console.error(`Cache cleanup: removed ${removedCount} old entries`);
        return removedCount;
    }
    async getStats() {
        await this.initialize();
        const entries = Object.values(this.index.entries);
        const stats = {
            totalEntries: entries.length,
            totalSize: 0,
            bySource: {},
            byLicense: {}
        };
        for (const entry of entries) {
            stats.totalSize += entry.fileSize;
            // By source
            if (!stats.bySource[entry.source]) {
                stats.bySource[entry.source] = { count: 0, size: 0 };
            }
            stats.bySource[entry.source].count++;
            stats.bySource[entry.source].size += entry.fileSize;
            // By license
            stats.byLicense[entry.license] = (stats.byLicense[entry.license] || 0) + 1;
        }
        return stats;
    }
    async verifyIntegrity() {
        await this.initialize();
        const entries = Object.values(this.index.entries);
        const invalid = [];
        let valid = 0;
        for (const entry of entries) {
            try {
                const currentHash = await this.calculateFileHash(entry.path);
                if (currentHash === entry.hash) {
                    valid++;
                }
                else {
                    invalid.push(`${entry.source}:${entry.id} - hash mismatch`);
                }
            }
            catch (error) {
                invalid.push(`${entry.source}:${entry.id} - file missing or unreadable`);
            }
        }
        return { valid, invalid };
    }
}
//# sourceMappingURL=sfx-cache.js.map