import { DownloadResult } from '../sfx-manager.js';
export interface CacheEntry {
    id: string;
    source: string;
    path: string;
    originalPath: string;
    metadata: any;
    license: string;
    hash: string;
    createdAt: string;
    lastAccessed: string;
    fileSize: number;
}
export interface CacheIndex {
    entries: {
        [key: string]: CacheEntry;
    };
    version: string;
    lastUpdated: string;
}
export declare class SfxCache {
    private cacheDir;
    private indexPath;
    private index;
    constructor(cacheDir?: string);
    initialize(): Promise<void>;
    private saveIndex;
    private getCacheKey;
    private calculateFileHash;
    get(id: string, source: string): Promise<DownloadResult | null>;
    set(id: string, source: string, result: DownloadResult): Promise<void>;
    list(source?: string): Promise<CacheEntry[]>;
    remove(id: string, source: string): Promise<boolean>;
    cleanup(maxAge?: number): Promise<number>;
    getStats(): Promise<{
        totalEntries: number;
        totalSize: number;
        bySource: {
            [source: string]: {
                count: number;
                size: number;
            };
        };
        byLicense: {
            [license: string]: number;
        };
    }>;
    verifyIntegrity(): Promise<{
        valid: number;
        invalid: string[];
    }>;
}
