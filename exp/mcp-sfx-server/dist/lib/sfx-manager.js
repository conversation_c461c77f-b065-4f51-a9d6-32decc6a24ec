import { promises as fs } from 'fs';
import path from 'path';
import { searchKenney, downloadKenney } from './providers/kenney.js';
import { searchFreesound, downloadFreesound } from './providers/freesound.js';
import { searchSonniss, downloadSonniss } from './providers/sonniss.js';
import { searchOpenGameArt, downloadOpenGameArt } from './providers/opengameart.js';
import { normalizeAudio } from './audio/normalizer.js';
import { SfxCache } from './cache/sfx-cache.js';
// Initialize cache
const cache = new SfxCache();
export async function searchSfx(params) {
    const { q, license = ["CC0", "CC-BY", "ROYALTY_FREE"], limit = 10 } = params;
    console.error(`Searching for SFX: "${q}" with licenses: ${license.join(', ')}`);
    const searchPromises = [
        searchKenney(q, limit),
        searchFreesound(q, license, Math.ceil(limit / 4)),
        searchSonniss(q, Math.ceil(limit / 4)),
        searchOpenGameArt(q, license, Math.ceil(limit / 4))
    ];
    try {
        const results = await Promise.allSettled(searchPromises);
        const allResults = [];
        results.forEach((result, index) => {
            const sources = ['kenney', 'freesound', 'sonniss', 'opengameart'];
            if (result.status === 'fulfilled') {
                allResults.push(...result.value);
            }
            else {
                console.error(`Search failed for ${sources[index]}:`, result.reason);
            }
        });
        // Sort by relevance and limit results
        const sortedResults = allResults
            .sort((a, b) => {
            // Prioritize CC0 licenses
            if (a.license === 'CC0' && b.license !== 'CC0')
                return -1;
            if (b.license === 'CC0' && a.license !== 'CC0')
                return 1;
            // Then by title relevance (simple keyword matching)
            const aRelevance = calculateRelevance(a.title, q);
            const bRelevance = calculateRelevance(b.title, q);
            return bRelevance - aRelevance;
        })
            .slice(0, limit);
        return {
            results: sortedResults,
            total: allResults.length
        };
    }
    catch (error) {
        console.error('Search error:', error);
        throw new Error(`Failed to search SFX: ${error}`);
    }
}
export async function downloadSfx(params) {
    const { id, source, format = "wav", mono = false } = params;
    console.error(`Downloading SFX: ${id} from ${source}`);
    // Check cache first
    const cached = await cache.get(id, source);
    if (cached) {
        console.error(`Found cached SFX: ${cached.path}`);
        return cached;
    }
    try {
        let rawPath;
        let metadata;
        switch (source) {
            case 'kenney':
                ({ path: rawPath, metadata } = await downloadKenney(id));
                break;
            case 'freesound':
                ({ path: rawPath, metadata } = await downloadFreesound(id));
                break;
            case 'sonniss':
                ({ path: rawPath, metadata } = await downloadSonniss(id));
                break;
            case 'opengameart':
                ({ path: rawPath, metadata } = await downloadOpenGameArt(id));
                break;
            default:
                throw new Error(`Unknown source: ${source}`);
        }
        // Normalize the audio
        const normalizedPath = await normalizeAudio(rawPath, {
            format,
            mono,
            sampleRate: 48000,
            peakDb: -3
        });
        const result = {
            path: normalizedPath,
            originalPath: rawPath,
            metadata: {
                id,
                source,
                license: metadata.license || 'UNKNOWN',
                title: metadata.title || 'Unknown',
                format,
                sampleRate: 48000,
                channels: mono ? 1 : (metadata.channels || 2),
                duration: metadata.duration || 0,
                peakDb: -3
            }
        };
        // Cache the result
        await cache.set(id, source, result);
        return result;
    }
    catch (error) {
        console.error('Download error:', error);
        throw new Error(`Failed to download SFX: ${error}`);
    }
}
export async function normalizeSfx(params) {
    const { inputPath, outputPath, format = "wav", mono = false, peakDb = -3 } = params;
    console.error(`Normalizing audio: ${inputPath}`);
    try {
        const normalizedPath = await normalizeAudio(inputPath, {
            outputPath,
            format,
            mono,
            sampleRate: 48000,
            peakDb
        });
        // Get basic metadata from the file
        const stats = await fs.stat(normalizedPath);
        const filename = path.basename(inputPath, path.extname(inputPath));
        return {
            path: normalizedPath,
            originalPath: inputPath,
            metadata: {
                id: filename,
                source: 'local',
                license: 'UNKNOWN',
                title: filename,
                format,
                sampleRate: 48000,
                channels: mono ? 1 : 2,
                duration: 0, // Would need audio analysis to get duration
                peakDb
            }
        };
    }
    catch (error) {
        console.error('Normalization error:', error);
        throw new Error(`Failed to normalize audio: ${error}`);
    }
}
function calculateRelevance(title, query) {
    const titleLower = title.toLowerCase();
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/);
    let score = 0;
    // Exact match gets highest score
    if (titleLower.includes(queryLower)) {
        score += 100;
    }
    // Individual word matches
    queryWords.forEach(word => {
        if (titleLower.includes(word)) {
            score += 10;
        }
    });
    return score;
}
//# sourceMappingURL=sfx-manager.js.map