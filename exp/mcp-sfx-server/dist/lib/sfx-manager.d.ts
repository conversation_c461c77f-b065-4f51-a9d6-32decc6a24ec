export interface SearchParams {
    q: string;
    license?: ("CC0" | "CC-BY" | "ROYALTY_FREE")[];
    limit?: number;
}
export interface DownloadParams {
    id: string;
    source: "kenney" | "sonniss" | "freesound" | "opengameart";
    format?: "wav" | "ogg";
    mono?: boolean;
}
export interface NormalizeParams {
    inputPath: string;
    outputPath?: string;
    format?: "wav" | "ogg";
    mono?: boolean;
    peakDb?: number;
}
export interface SfxResult {
    id: string;
    title: string;
    description?: string;
    source: string;
    license: string;
    url?: string;
    tags?: string[];
    duration?: number;
    fileSize?: number;
}
export interface DownloadResult {
    path: string;
    originalPath: string;
    metadata: {
        id: string;
        source: string;
        license: string;
        title: string;
        format: string;
        sampleRate: number;
        channels: number;
        duration: number;
        peakDb: number;
    };
}
export declare function searchSfx(params: SearchParams): Promise<{
    results: SfxResult[];
    total: number;
}>;
export declare function downloadSfx(params: DownloadParams): Promise<DownloadResult>;
export declare function normalizeSfx(params: NormalizeParams): Promise<DownloadResult>;
