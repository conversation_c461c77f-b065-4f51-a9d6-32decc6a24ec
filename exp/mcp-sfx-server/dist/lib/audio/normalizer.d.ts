export interface NormalizationOptions {
    outputPath?: string;
    format?: 'wav' | 'ogg';
    mono?: boolean;
    sampleRate?: number;
    peakDb?: number;
}
export declare function normalizeAudio(inputPath: string, options?: NormalizationOptions): Promise<string>;
export declare function getAudioInfo(filePath: string): Promise<{
    duration: number;
    sampleRate: number;
    channels: number;
    format: string;
    bitrate?: number;
}>;
export declare function convertFormat(inputPath: string, outputFormat: 'wav' | 'ogg' | 'mp3', outputPath?: string): Promise<string>;
