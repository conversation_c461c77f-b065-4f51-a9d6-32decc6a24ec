import ffmpeg from 'fluent-ffmpeg';
import { promises as fs } from 'fs';
import path from 'path';
export async function normalizeAudio(inputPath, options = {}) {
    const { outputPath, format = 'wav', mono = false, sampleRate = 48000, peakDb = -3 } = options;
    // Generate output path if not provided
    const finalOutputPath = outputPath || generateOutputPath(inputPath, format);
    // Ensure output directory exists
    await fs.mkdir(path.dirname(finalOutputPath), { recursive: true });
    console.error(`Normalizing audio: ${inputPath} -> ${finalOutputPath}`);
    console.error(`Options: ${JSON.stringify({ format, mono, sampleRate, peakDb })}`);
    return new Promise((resolve, reject) => {
        let command = ffmpeg(inputPath);
        // Set audio codec based on format
        if (format === 'ogg') {
            command = command.audioCodec('libvorbis');
        }
        else {
            command = command.audioCodec('pcm_s16le');
        }
        // Set sample rate
        command = command.audioFrequency(sampleRate);
        // Set channels (mono/stereo)
        if (mono) {
            command = command.audioChannels(1);
        }
        // Audio filters for normalization
        const filters = [];
        // Normalize peak level
        filters.push(`loudnorm=I=-16:TP=${peakDb}:LRA=11`);
        // Apply high-pass filter to remove DC offset and low-frequency noise
        filters.push('highpass=f=20');
        // Apply gentle low-pass filter to remove high-frequency noise
        filters.push('lowpass=f=20000');
        if (filters.length > 0) {
            command = command.audioFilters(filters);
        }
        // Set output format
        command = command.format(format);
        // Configure output
        command = command.output(finalOutputPath);
        // Handle events
        command.on('start', (commandLine) => {
            console.error(`FFmpeg command: ${commandLine}`);
        });
        command.on('progress', (progress) => {
            if (progress.percent) {
                console.error(`Processing: ${Math.round(progress.percent)}%`);
            }
        });
        command.on('end', () => {
            console.error(`Audio normalization completed: ${finalOutputPath}`);
            resolve(finalOutputPath);
        });
        command.on('error', (error) => {
            console.error(`FFmpeg error: ${error.message}`);
            reject(new Error(`Audio normalization failed: ${error.message}`));
        });
        // Start processing
        command.run();
    });
}
function generateOutputPath(inputPath, format) {
    const inputDir = path.dirname(inputPath);
    const inputName = path.basename(inputPath, path.extname(inputPath));
    const timestamp = Date.now();
    return path.join(inputDir, `${inputName}_normalized_${timestamp}.${format}`);
}
export async function getAudioInfo(filePath) {
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(filePath, (error, metadata) => {
            if (error) {
                reject(new Error(`Failed to get audio info: ${error.message}`));
                return;
            }
            const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
            if (!audioStream) {
                reject(new Error('No audio stream found'));
                return;
            }
            resolve({
                duration: metadata.format.duration || 0,
                sampleRate: audioStream.sample_rate || 0,
                channels: audioStream.channels || 0,
                format: audioStream.codec_name || 'unknown',
                bitrate: audioStream.bit_rate ? parseInt(audioStream.bit_rate) : undefined
            });
        });
    });
}
export async function convertFormat(inputPath, outputFormat, outputPath) {
    const finalOutputPath = outputPath || generateOutputPath(inputPath, outputFormat);
    return new Promise((resolve, reject) => {
        let command = ffmpeg(inputPath);
        // Set codec based on format
        switch (outputFormat) {
            case 'ogg':
                command = command.audioCodec('libvorbis');
                break;
            case 'mp3':
                command = command.audioCodec('libmp3lame');
                break;
            case 'wav':
                command = command.audioCodec('pcm_s16le');
                break;
        }
        command
            .format(outputFormat)
            .output(finalOutputPath)
            .on('end', () => {
            console.error(`Format conversion completed: ${finalOutputPath}`);
            resolve(finalOutputPath);
        })
            .on('error', (error) => {
            console.error(`Format conversion error: ${error.message}`);
            reject(new Error(`Format conversion failed: ${error.message}`));
        })
            .run();
    });
}
//# sourceMappingURL=normalizer.js.map