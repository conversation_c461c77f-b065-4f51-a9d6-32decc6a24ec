{"version": 3, "file": "sfx-manager.js", "sourceRoot": "", "sources": ["../../lib/sfx-manager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACrE,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC9E,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACxE,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AACpF,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAmDhD,mBAAmB;AACnB,MAAM,KAAK,GAAG,IAAI,QAAQ,EAAE,CAAC;AAE7B,MAAM,CAAC,KAAK,UAAU,SAAS,CAAC,MAAoB;IAClD,MAAM,EAAE,CAAC,EAAE,OAAO,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC;IAE7E,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,oBAAoB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEhF,MAAM,cAAc,GAAG;QACrB,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC;QACtB,eAAe,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACjD,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACtC,iBAAiB,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;KACpD,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACzD,MAAM,UAAU,GAAgB,EAAE,CAAC;QAEnC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChC,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAClE,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClC,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,qBAAqB,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,MAAM,aAAa,GAAG,UAAU;aAC7B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACb,0BAA0B;YAC1B,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK;gBAAE,OAAO,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK;gBAAE,OAAO,CAAC,CAAC;YAEzD,oDAAoD;YACpD,MAAM,UAAU,GAAG,kBAAkB,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,kBAAkB,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClD,OAAO,UAAU,GAAG,UAAU,CAAC;QACjC,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnB,OAAO;YACL,OAAO,EAAE,aAAa;YACtB,KAAK,EAAE,UAAU,CAAC,MAAM;SACzB,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;IACpD,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,MAAsB;IACtD,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC;IAE5D,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,CAAC,CAAC;IAEvD,oBAAoB;IACpB,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3C,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,CAAC,KAAK,CAAC,qBAAqB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,CAAC;QACH,IAAI,OAAe,CAAC;QACpB,IAAI,QAAa,CAAC;QAElB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,QAAQ;gBACX,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,WAAW;gBACd,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,MAAM;YACR,KAAK,SAAS;gBACZ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,aAAa;gBAChB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9D,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,sBAAsB;QACtB,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE;YACnD,MAAM;YACN,IAAI;YACJ,UAAU,EAAE,KAAK;YACjB,MAAM,EAAE,CAAC,CAAC;SACX,CAAC,CAAC;QAEH,MAAM,MAAM,GAAmB;YAC7B,IAAI,EAAE,cAAc;YACpB,YAAY,EAAE,OAAO;YACrB,QAAQ,EAAE;gBACR,EAAE;gBACF,MAAM;gBACN,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,SAAS;gBACtC,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,SAAS;gBAClC,MAAM;gBACN,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC;gBAC7C,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,CAAC;gBAChC,MAAM,EAAE,CAAC,CAAC;aACX;SACF,CAAC;QAEF,mBAAmB;QACnB,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAEpC,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACxC,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,YAAY,CAAC,MAAuB;IACxD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC;IAEpF,OAAO,CAAC,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;IAEjD,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,SAAS,EAAE;YACrD,UAAU;YACV,MAAM;YACN,IAAI;YACJ,UAAU,EAAE,KAAK;YACjB,MAAM;SACP,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;QAEnE,OAAO;YACL,IAAI,EAAE,cAAc;YACpB,YAAY,EAAE,SAAS;YACvB,QAAQ,EAAE;gBACR,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,QAAQ;gBACf,MAAM;gBACN,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,QAAQ,EAAE,CAAC,EAAE,4CAA4C;gBACzD,MAAM;aACP;SACF,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAa,EAAE,KAAa;IACtD,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IACvC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IACvC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAE3C,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,iCAAiC;IACjC,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACpC,KAAK,IAAI,GAAG,CAAC;IACf,CAAC;IAED,0BAA0B;IAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACxB,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC"}